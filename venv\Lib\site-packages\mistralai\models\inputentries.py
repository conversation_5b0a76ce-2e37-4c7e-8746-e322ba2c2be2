"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .functionresultentry import FunctionResultEntry, FunctionResultEntryTypedDict
from .messageinputentry import MessageInputEntry, MessageInputEntryTypedDict
from typing import Union
from typing_extensions import TypeAliasType


InputEntriesTypedDict = TypeAliasType(
    "InputEntriesTypedDict",
    Union[MessageInputEntryTypedDict, FunctionResultEntryTypedDict],
)


InputEntries = TypeAliasType(
    "InputEntries", Union[MessageInputEntry, FunctionResultEntry]
)
